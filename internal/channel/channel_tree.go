package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
)

type Tree Node

// GetAllDescendantIDs 递归获取当前节点及其所有子节点的用户ID列表
func (t *Tree) GetAllDescendantIDs() []uint64 {
	// 将Tree转为Node，使用Node的方法
	return (*Node)(t).GetAllDescendantIDs()
}

// GetChannelTree 获取完整的渠道树结构
// userId: 用户ID，作为查询的起始点
// 返回该用户所在树的完整结构
func GetChannelTree(userId uint64) (c *Tree, err error) {
	return getChannelTreeWithMode(userId, false)
}

// GetChannelChildren 获取当前节点及其所有下级节点（包括间接下级）
// userId: 用户ID，作为查询的父节点
// 返回该用户及其所有下级节点构成的子树
func GetChannelChildren(userId uint64) (c *Tree, err error) {
	return getChannelTreeWithMode(userId, true)
}

// getAllDescendantChannels 递归获取指定用户的所有下级渠道记录（使用双向链表优化）
func getAllDescendantChannels(userId uint64, visited map[uint64]bool) ([]*model.Channel, error) {
	service := NewBidirectionalChannelService()
	return service.GetAllDescendants(userId)
}

// getChannelTreeWithMode 根据不同模式获取渠道树
// userId: 用户ID
// onlyChildren: 是否只获取子节点树
// - true: 返回当前节点及其所有下级节点（包括间接下级）
// - false: 返回完整树结构
func getChannelTreeWithMode(userId uint64, onlyChildren bool) (c *Tree, err error) {
	q := query.Channel

	var chs []*model.Channel
	var user *model.User

	if onlyChildren {
		// 仅子节点模式：递归查询所有下级节点
		chs, err = getAllDescendantChannels(userId, nil)
		if err != nil {
			return nil, err
		}

		// 获取当前用户信息
		u := query.User
		user, err = u.FirstByID(userId)
		if err != nil {
			return nil, err
		}
	} else {
		// 完整树模式：查询整个树
		// 首先确保用户有频道记录
		err = ensureChannelRecord(userId)
		if err != nil {
			return nil, err
		}

		// 获取用户的频道记录
		userChannel, err := q.Where(q.UserID.Eq(userId)).Preload(q.User, q.ParentUser).First()
		if err != nil {
			return nil, err
		}

		// 找到根节点
		rootUserId := findRootUser(userChannel)

		// 查询整个树的所有节点
		chs, err = getAllTreeNodes(rootUserId)
		if err != nil {
			return nil, err
		}

		userId = rootUserId
	}

	return buildChannelTree(userId, chs, user, onlyChildren)
}

// ensureChannelRecord 确保用户有频道记录
func ensureChannelRecord(userId uint64) error {
	service := NewBidirectionalChannelService()
	q := service.q.Channel

	// 检查是否已存在记录
	_, err := q.Where(q.UserID.Eq(userId)).First()
	if err == nil {
		return nil // 记录已存在
	}

	// 创建新记录
	channel := &model.Channel{
		UserID:       userId,
		ParentUserID: 0, // 默认为根节点
	}

	return service.db.Create(channel).Error
}

// findRootUser 找到树的根节点用户ID（使用双向链表优化）
func findRootUser(channel *model.Channel) uint64 {
	service := NewBidirectionalChannelService()
	path, err := service.GetPathToRoot(channel.UserID)
	if err != nil || len(path) == 0 {
		return channel.UserID
	}
	return path[0].UserID // 路径的第一个节点就是根节点
}

// getAllTreeNodes 获取整个树的所有节点（使用双向链表优化）
func getAllTreeNodes(rootUserId uint64) ([]*model.Channel, error) {
	service := NewBidirectionalChannelService()

	// 获取根节点
	rootNode, err := service.GetChannelWithBidirectionalInfo(rootUserId)
	if err != nil {
		return nil, err
	}

	// 获取所有后代节点
	descendants, err := service.GetAllDescendants(rootUserId)
	if err != nil {
		return nil, err
	}

	// 合并根节点和所有后代节点
	allNodes := []*model.Channel{rootNode}
	allNodes = append(allNodes, descendants...)

	return allNodes, nil
}

// buildChannelTree 构建渠道树结构
func buildChannelTree(userId uint64, chs []*model.Channel, user *model.User, onlyChildren bool) (*Tree, error) {
	// 用于构建树的节点映射
	channelMap := make(map[uint64]*Node)

	if onlyChildren {
		// 仅子节点模式：构建当前节点作为根节点
		if user != nil {
			// 创建当前节点
			channelMap[userId] = &Node{
				Name:      user.Name,
				RootID:    userId, // 在仅子节点模式下，当前节点就是根节点
				CurrentID: userId,
				Children:  make([]*Node, 0),
			}
		}

		// 先创建所有节点
		for _, v := range chs {
			if v.User != nil && channelMap[v.UserID] == nil {
				channelMap[v.UserID] = &Node{
					Name:      v.User.Name,
					CurrentID: v.UserID,
					RootID:    userId, // 在仅子节点模式下，设置当前查询的userId为根
					Children:  make([]*Node, 0),
				}
			}
		}

		// 然后构建父子关系：在邻接表模式下，parent_user_id 指向父节点
		for _, v := range chs {
			if v.ParentUserID != 0 && channelMap[v.ParentUserID] != nil && channelMap[v.UserID] != nil {
				// 将当前节点添加到父节点的子节点列表中
				channelMap[v.ParentUserID].Children = append(channelMap[v.ParentUserID].Children, channelMap[v.UserID])
			}
		}
	} else {
		// 完整树模式：先创建所有节点
		for _, v := range chs {
			if v.User != nil && channelMap[v.UserID] == nil {
				channelMap[v.UserID] = &Node{
					Name:      v.User.Name,
					RootID:    userId, // 根节点ID
					CurrentID: v.UserID,
					Children:  make([]*Node, 0),
				}
			}
		}

		// 然后构建节点之间的父子关系
		for _, v := range chs {
			if v.ParentUserID != 0 && channelMap[v.ParentUserID] != nil && channelMap[v.UserID] != nil {
				// 将当前节点添加到父节点的子节点列表中
				channelMap[v.ParentUserID].Children = append(channelMap[v.ParentUserID].Children, channelMap[v.UserID])
			}
		}
	}

	return (*Tree)(channelMap[userId]), nil
}
