package channel

import (
	"testing"

	"git.uozi.org/uozi/awm-api/model"
)

// TestGetAllDescendantChannels 测试递归获取所有下级渠道记录
func TestGetAllDescendantChannels(t *testing.T) {
	// 这里需要根据实际的数据库连接和测试数据来编写测试
	// 由于涉及数据库操作，建议在集成测试中进行
	t.<PERSON>("需要数据库连接的集成测试")
}

// TestBuildChannelTree 测试构建渠道树结构
func TestBuildChannelTree(t *testing.T) {
	// 模拟测试数据
	testChannels := []*model.Channel{
		{
			UserID:       1,
			User:         &model.User{Name: "用户1"},
			ParentUserID: 0, // 根节点
		},
		{
			UserID:       2,
			User:         &model.User{Name: "用户2"},
			ParentUserID: 1, // 父节点是用户1
		},
		{
			UserID:       3,
			User:         &model.User{Name: "用户3"},
			ParentUserID: 2, // 父节点是用户2
		},
		{
			UserID:       4,
			User:         &model.User{Name: "用户4"},
			ParentUserID: 2, // 父节点是用户2
		},
	}

	testUser := &model.User{Name: "用户1"}

	// 测试 onlyChildren 模式
	tree, err := buildChannelTree(1, testChannels, testUser, true)
	if err != nil {
		t.Fatalf("构建渠道树失败: %v", err)
	}

	if tree == nil {
		t.Fatal("返回的树为空")
	}

	// 验证根节点
	if tree.CurrentID != 1 {
		t.Errorf("期望根节点ID为1，实际为%d", tree.CurrentID)
	}

	if tree.Name != "用户1" {
		t.Errorf("期望根节点名称为'用户1'，实际为'%s'", tree.Name)
	}

	// 验证子节点数量
	if len(tree.Children) != 1 {
		t.Errorf("期望根节点有1个直接子节点，实际有%d个", len(tree.Children))
	}

	// 验证第一级子节点
	if len(tree.Children) > 0 {
		child := tree.Children[0]
		if child.CurrentID != 2 {
			t.Errorf("期望第一个子节点ID为2，实际为%d", child.CurrentID)
		}

		// 验证第二级子节点
		if len(child.Children) != 2 {
			t.Errorf("期望第一个子节点有2个子节点，实际有%d个", len(child.Children))
		}
	}

	t.Log("渠道树构建测试通过")
}

// TestChannelTreeMethods 测试树的方法
func TestChannelTreeMethods(t *testing.T) {
	// 创建测试树结构
	tree := &Tree{
		CurrentID: 1,
		Name:      "根节点",
		Children: []*Node{
			{
				CurrentID: 2,
				Name:      "子节点1",
				Children: []*Node{
					{CurrentID: 4, Name: "孙节点1"},
					{CurrentID: 5, Name: "孙节点2"},
				},
			},
			{
				CurrentID: 3,
				Name:      "子节点2",
				Children: []*Node{
					{CurrentID: 6, Name: "孙节点3"},
				},
			},
		},
	}

	// 测试 GetAllDescendantIDs 方法
	ids := tree.GetAllDescendantIDs()
	expectedIDs := []uint64{1, 2, 4, 5, 3, 6}

	if len(ids) != len(expectedIDs) {
		t.Errorf("期望获取%d个ID，实际获取%d个", len(expectedIDs), len(ids))
	}

	// 验证所有ID都存在
	idMap := make(map[uint64]bool)
	for _, id := range ids {
		idMap[id] = true
	}

	for _, expectedID := range expectedIDs {
		if !idMap[expectedID] {
			t.Errorf("缺少期望的ID: %d", expectedID)
		}
	}

	t.Log("树方法测试通过")
}

// TestBidirectionalChannelService 测试双向链表频道服务
func TestBidirectionalChannelService(t *testing.T) {
	// 这里需要数据库连接，跳过单元测试
	t.Skip("需要数据库连接的集成测试")

	// 以下是集成测试的示例代码
	/*
		service := NewBidirectionalChannelService()

		// 测试获取直接子节点
		children, err := service.GetDirectChildren(1)
		if err != nil {
			t.Fatalf("获取直接子节点失败: %v", err)
		}
		t.Logf("获取到 %d 个直接子节点", len(children))

		// 测试获取所有祖先节点
		ancestors, err := service.GetAllAncestors(3)
		if err != nil {
			t.Fatalf("获取祖先节点失败: %v", err)
		}
		t.Logf("获取到 %d 个祖先节点", len(ancestors))

		// 测试获取所有后代节点
		descendants, err := service.GetAllDescendants(1)
		if err != nil {
			t.Fatalf("获取后代节点失败: %v", err)
		}
		t.Logf("获取到 %d 个后代节点", len(descendants))

		// 测试获取兄弟节点
		siblings, err := service.GetSiblings(3)
		if err != nil {
			t.Fatalf("获取兄弟节点失败: %v", err)
		}
		t.Logf("获取到 %d 个兄弟节点", len(siblings))

		// 测试获取到根节点的路径
		path, err := service.GetPathToRoot(4)
		if err != nil {
			t.Fatalf("获取路径失败: %v", err)
		}
		t.Logf("获取到长度为 %d 的路径", len(path))
	*/
}

// TestChannelModelMethods 测试频道模型的方法
func TestChannelModelMethods(t *testing.T) {
	// 创建测试频道
	channel := &model.Channel{
		UserID:          1,
		ParentUserID:    0,
		ChildrenUserIDs: model.ChildrenUserIDs{2, 3, 4},
		NextSiblingID:   0,
		PrevSiblingID:   0,
		ChildCount:      3,
	}

	// 测试 GetChildren 方法
	children := channel.GetChildren()
	expectedChildren := []uint64{2, 3, 4}
	if len(children) != len(expectedChildren) {
		t.Errorf("期望 %d 个子节点，实际 %d 个", len(expectedChildren), len(children))
	}

	for i, childID := range children {
		if childID != expectedChildren[i] {
			t.Errorf("期望子节点ID %d，实际 %d", expectedChildren[i], childID)
		}
	}

	// 测试 HasChildren 方法
	if !channel.HasChildren() {
		t.Error("期望有子节点，实际没有")
	}

	// 测试 IsRoot 方法
	if !channel.IsRoot() {
		t.Error("期望是根节点，实际不是")
	}

	// 测试 AddChild 方法
	channel.AddChild(5)
	if channel.ChildCount != 4 {
		t.Errorf("期望子节点数量为 4，实际为 %d", channel.ChildCount)
	}

	// 测试重复添加
	channel.AddChild(5)
	if channel.ChildCount != 4 {
		t.Errorf("重复添加后期望子节点数量仍为 4，实际为 %d", channel.ChildCount)
	}

	// 测试 RemoveChild 方法
	channel.RemoveChild(3)
	if channel.ChildCount != 3 {
		t.Errorf("删除后期望子节点数量为 3，实际为 %d", channel.ChildCount)
	}

	t.Log("频道模型方法测试通过")
}
