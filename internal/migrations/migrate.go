package migrate

import (
	"encoding/json"
	"fmt"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

var Migrations = []*gormigrate.Migration{
	{
		ID: "20241201_refactor_channel_to_adjacency_list",
		Migrate: func(tx *gorm.DB) error {
			// 1. 检查并添加 parent_user_id 字段到 channels 表
			var count int64
			tx.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'channels' AND column_name = 'parent_user_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD COLUMN parent_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '父用户ID'").Error; err != nil {
					return err
				}
			}

			// 2. 检查并添加索引
			tx.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'channels' AND index_name = 'idx_parent_user_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD INDEX idx_parent_user_id (parent_user_id)").Error; err != nil {
					return err
				}
			}

			// 3. 数据迁移：将现有的 relate_user_id 关系转换为 parent_user_id 关系
			// 在原有结构中，如果 A 的 relate_user_id 是 B，意味着 B 是 A 的下级
			// 在新结构中，B 的 parent_user_id 应该是 A
			if err := tx.Exec(`
				UPDATE channels c1
				SET parent_user_id = (
					SELECT c2.user_id
					FROM channels c2
					WHERE c2.relate_user_id = c1.user_id
					AND c2.deleted_at = 0
					LIMIT 1
				)
				WHERE c1.deleted_at = 0
			`).Error; err != nil {
				return err
			}

			// 4. 为每个用户创建频道记录（如果不存在）
			// 确保所有用户都有对应的频道记录，parent_user_id 默认为 0（根节点）
			if err := tx.Exec(`
				INSERT INTO channels (user_id, parent_user_id, created_at, updated_at)
				SELECT u.id, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
				FROM users u
				WHERE u.deleted_at = 0
				AND u.id NOT IN (SELECT user_id FROM channels WHERE deleted_at = 0)
			`).Error; err != nil {
				return err
			}

			// 5. 删除 channels_view 视图（如果存在）
			tx.Exec("DROP VIEW IF EXISTS channels_view")

			return nil
		},
		Rollback: func(tx *gorm.DB) error {
			// 回滚操作：删除 parent_user_id 字段
			if err := tx.Exec("ALTER TABLE channels DROP INDEX idx_parent_user_id").Error; err != nil {
				return err
			}
			if err := tx.Exec("ALTER TABLE channels DROP COLUMN parent_user_id").Error; err != nil {
				return err
			}
			return nil
		},
	},
	{
		ID: "20241202_add_bidirectional_channel_structure",
		Migrate: func(tx *gorm.DB) error {
			var count int64

			// 1. 添加 children_user_ids JSON 字段存储子节点ID列表
			tx.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'channels' AND column_name = 'children_user_ids'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD COLUMN children_user_ids JSON COMMENT '子用户ID列表'").Error; err != nil {
					return err
				}
			}

			// 2. 添加 next_sibling_id 字段
			tx.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'channels' AND column_name = 'next_sibling_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD COLUMN next_sibling_id BIGINT UNSIGNED DEFAULT 0 COMMENT '下一个兄弟节点用户ID'").Error; err != nil {
					return err
				}
			}

			// 3. 添加 prev_sibling_id 字段
			tx.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'channels' AND column_name = 'prev_sibling_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD COLUMN prev_sibling_id BIGINT UNSIGNED DEFAULT 0 COMMENT '上一个兄弟节点用户ID'").Error; err != nil {
					return err
				}
			}

			// 4. 添加 child_count 字段缓存子节点数量
			tx.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'channels' AND column_name = 'child_count'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD COLUMN child_count INT DEFAULT 0 COMMENT '子节点数量'").Error; err != nil {
					return err
				}
			}

			// 5. 添加相关索引
			tx.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'channels' AND index_name = 'idx_next_sibling_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD INDEX idx_next_sibling_id (next_sibling_id)").Error; err != nil {
					return err
				}
			}

			tx.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'channels' AND index_name = 'idx_prev_sibling_id'").Scan(&count)
			if count == 0 {
				if err := tx.Exec("ALTER TABLE channels ADD INDEX idx_prev_sibling_id (prev_sibling_id)").Error; err != nil {
					return err
				}
			}

			// 6. 初始化双向链表数据
			if err := initializeBidirectionalData(tx); err != nil {
				return err
			}

			return nil
		},
		Rollback: func(tx *gorm.DB) error {
			// 回滚操作：删除新添加的字段和索引
			tx.Exec("ALTER TABLE channels DROP INDEX idx_next_sibling_id")
			tx.Exec("ALTER TABLE channels DROP INDEX idx_prev_sibling_id")
			tx.Exec("ALTER TABLE channels DROP COLUMN children_user_ids")
			tx.Exec("ALTER TABLE channels DROP COLUMN next_sibling_id")
			tx.Exec("ALTER TABLE channels DROP COLUMN prev_sibling_id")
			tx.Exec("ALTER TABLE channels DROP COLUMN child_count")
			return nil
		},
	},
}

// initializeBidirectionalData 初始化双向链表数据
func initializeBidirectionalData(tx *gorm.DB) error {
	// 1. 构建每个父节点的子节点列表
	type ChannelData struct {
		UserID       uint64 `json:"user_id"`
		ParentUserID uint64 `json:"parent_user_id"`
	}

	var channels []ChannelData
	if err := tx.Raw("SELECT user_id, parent_user_id FROM channels WHERE deleted_at = 0 ORDER BY parent_user_id, user_id").Scan(&channels).Error; err != nil {
		return err
	}

	// 构建父子关系映射
	parentChildrenMap := make(map[uint64][]uint64)
	for _, ch := range channels {
		if ch.ParentUserID != 0 {
			parentChildrenMap[ch.ParentUserID] = append(parentChildrenMap[ch.ParentUserID], ch.UserID)
		}
	}

	// 2. 更新每个节点的 children_user_ids 和 child_count
	for parentID, childrenIDs := range parentChildrenMap {
		childrenJSON, err := json.Marshal(childrenIDs)
		if err != nil {
			return fmt.Errorf("序列化子节点列表失败: %v", err)
		}

		if err := tx.Exec("UPDATE channels SET children_user_ids = ?, child_count = ? WHERE user_id = ? AND deleted_at = 0",
			string(childrenJSON), len(childrenIDs), parentID).Error; err != nil {
			return err
		}

		// 3. 构建兄弟节点的双向链表
		if len(childrenIDs) > 1 {
			for i, childID := range childrenIDs {
				var nextSiblingID, prevSiblingID uint64

				if i > 0 {
					prevSiblingID = childrenIDs[i-1]
				}
				if i < len(childrenIDs)-1 {
					nextSiblingID = childrenIDs[i+1]
				}

				if err := tx.Exec("UPDATE channels SET next_sibling_id = ?, prev_sibling_id = ? WHERE user_id = ? AND deleted_at = 0",
					nextSiblingID, prevSiblingID, childID).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
}
