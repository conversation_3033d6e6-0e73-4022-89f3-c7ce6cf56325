// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newChannel(db *gorm.DB, opts ...gen.DOOption) channel {
	_channel := channel{}

	_channel.channelDo.UseDB(db, opts...)
	_channel.channelDo.UseModel(&model.Channel{})

	tableName := _channel.channelDo.TableName()
	_channel.ALL = field.NewAsterisk(tableName)
	_channel.ID = field.NewUint64(tableName, "id")
	_channel.CreatedAt = field.NewUint64(tableName, "created_at")
	_channel.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_channel.DeletedAt = field.NewUint(tableName, "deleted_at")
	_channel.UserID = field.NewUint64(tableName, "user_id")
	_channel.ParentUserID = field.NewUint64(tableName, "parent_user_id")
	_channel.ChildrenUserIDs = field.NewField(tableName, "children_user_ids")
	_channel.NextSiblingID = field.NewUint64(tableName, "next_sibling_id")
	_channel.PrevSiblingID = field.NewUint64(tableName, "prev_sibling_id")
	_channel.ChildCount = field.NewInt(tableName, "child_count")
	_channel.User = channelBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.UserGroup", "model.UserGroup"),
		},
	}

	_channel.ParentUser = channelBelongsToParentUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ParentUser", "model.User"),
	}

	_channel.NextSibling = channelBelongsToNextSibling{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("NextSibling", "model.User"),
	}

	_channel.PrevSibling = channelBelongsToPrevSibling{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("PrevSibling", "model.User"),
	}

	_channel.fillFieldMap()

	return _channel
}

type channel struct {
	channelDo

	ALL             field.Asterisk
	ID              field.Uint64
	CreatedAt       field.Uint64
	UpdatedAt       field.Uint64
	DeletedAt       field.Uint
	UserID          field.Uint64 // 用户ID
	ParentUserID    field.Uint64 // 父用户ID
	ChildrenUserIDs field.Field  // 子用户ID列表
	NextSiblingID   field.Uint64 // 下一个兄弟节点用户ID
	PrevSiblingID   field.Uint64 // 上一个兄弟节点用户ID
	ChildCount      field.Int    // 子节点数量
	User            channelBelongsToUser

	ParentUser channelBelongsToParentUser

	NextSibling channelBelongsToNextSibling

	PrevSibling channelBelongsToPrevSibling

	fieldMap map[string]field.Expr
}

func (c channel) Table(newTableName string) *channel {
	c.channelDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c channel) As(alias string) *channel {
	c.channelDo.DO = *(c.channelDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *channel) updateTableName(table string) *channel {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.UserID = field.NewUint64(table, "user_id")
	c.ParentUserID = field.NewUint64(table, "parent_user_id")
	c.ChildrenUserIDs = field.NewField(table, "children_user_ids")
	c.NextSiblingID = field.NewUint64(table, "next_sibling_id")
	c.PrevSiblingID = field.NewUint64(table, "prev_sibling_id")
	c.ChildCount = field.NewInt(table, "child_count")

	c.fillFieldMap()

	return c
}

func (c *channel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *channel) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 14)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["user_id"] = c.UserID
	c.fieldMap["parent_user_id"] = c.ParentUserID
	c.fieldMap["children_user_ids"] = c.ChildrenUserIDs
	c.fieldMap["next_sibling_id"] = c.NextSiblingID
	c.fieldMap["prev_sibling_id"] = c.PrevSiblingID
	c.fieldMap["child_count"] = c.ChildCount

}

func (c channel) clone(db *gorm.DB) channel {
	c.channelDo.ReplaceConnPool(db.Statement.ConnPool)
	c.User.db = db.Session(&gorm.Session{Initialized: true})
	c.User.db.Statement.ConnPool = db.Statement.ConnPool
	c.ParentUser.db = db.Session(&gorm.Session{Initialized: true})
	c.ParentUser.db.Statement.ConnPool = db.Statement.ConnPool
	c.NextSibling.db = db.Session(&gorm.Session{Initialized: true})
	c.NextSibling.db.Statement.ConnPool = db.Statement.ConnPool
	c.PrevSibling.db = db.Session(&gorm.Session{Initialized: true})
	c.PrevSibling.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c channel) replaceDB(db *gorm.DB) channel {
	c.channelDo.ReplaceDB(db)
	c.User.db = db.Session(&gorm.Session{})
	c.ParentUser.db = db.Session(&gorm.Session{})
	c.NextSibling.db = db.Session(&gorm.Session{})
	c.PrevSibling.db = db.Session(&gorm.Session{})
	return c
}

type channelBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a channelBelongsToUser) Where(conds ...field.Expr) *channelBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelBelongsToUser) WithContext(ctx context.Context) *channelBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelBelongsToUser) Session(session *gorm.Session) *channelBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a channelBelongsToUser) Model(m *model.Channel) *channelBelongsToUserTx {
	return &channelBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a channelBelongsToUser) Unscoped() *channelBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type channelBelongsToUserTx struct{ tx *gorm.Association }

func (a channelBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a channelBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a channelBelongsToUserTx) Unscoped() *channelBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelBelongsToParentUser struct {
	db *gorm.DB

	field.RelationField
}

func (a channelBelongsToParentUser) Where(conds ...field.Expr) *channelBelongsToParentUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelBelongsToParentUser) WithContext(ctx context.Context) *channelBelongsToParentUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelBelongsToParentUser) Session(session *gorm.Session) *channelBelongsToParentUser {
	a.db = a.db.Session(session)
	return &a
}

func (a channelBelongsToParentUser) Model(m *model.Channel) *channelBelongsToParentUserTx {
	return &channelBelongsToParentUserTx{a.db.Model(m).Association(a.Name())}
}

func (a channelBelongsToParentUser) Unscoped() *channelBelongsToParentUser {
	a.db = a.db.Unscoped()
	return &a
}

type channelBelongsToParentUserTx struct{ tx *gorm.Association }

func (a channelBelongsToParentUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelBelongsToParentUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelBelongsToParentUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelBelongsToParentUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelBelongsToParentUserTx) Clear() error {
	return a.tx.Clear()
}

func (a channelBelongsToParentUserTx) Count() int64 {
	return a.tx.Count()
}

func (a channelBelongsToParentUserTx) Unscoped() *channelBelongsToParentUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelBelongsToNextSibling struct {
	db *gorm.DB

	field.RelationField
}

func (a channelBelongsToNextSibling) Where(conds ...field.Expr) *channelBelongsToNextSibling {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelBelongsToNextSibling) WithContext(ctx context.Context) *channelBelongsToNextSibling {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelBelongsToNextSibling) Session(session *gorm.Session) *channelBelongsToNextSibling {
	a.db = a.db.Session(session)
	return &a
}

func (a channelBelongsToNextSibling) Model(m *model.Channel) *channelBelongsToNextSiblingTx {
	return &channelBelongsToNextSiblingTx{a.db.Model(m).Association(a.Name())}
}

func (a channelBelongsToNextSibling) Unscoped() *channelBelongsToNextSibling {
	a.db = a.db.Unscoped()
	return &a
}

type channelBelongsToNextSiblingTx struct{ tx *gorm.Association }

func (a channelBelongsToNextSiblingTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelBelongsToNextSiblingTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelBelongsToNextSiblingTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelBelongsToNextSiblingTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelBelongsToNextSiblingTx) Clear() error {
	return a.tx.Clear()
}

func (a channelBelongsToNextSiblingTx) Count() int64 {
	return a.tx.Count()
}

func (a channelBelongsToNextSiblingTx) Unscoped() *channelBelongsToNextSiblingTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelBelongsToPrevSibling struct {
	db *gorm.DB

	field.RelationField
}

func (a channelBelongsToPrevSibling) Where(conds ...field.Expr) *channelBelongsToPrevSibling {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelBelongsToPrevSibling) WithContext(ctx context.Context) *channelBelongsToPrevSibling {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelBelongsToPrevSibling) Session(session *gorm.Session) *channelBelongsToPrevSibling {
	a.db = a.db.Session(session)
	return &a
}

func (a channelBelongsToPrevSibling) Model(m *model.Channel) *channelBelongsToPrevSiblingTx {
	return &channelBelongsToPrevSiblingTx{a.db.Model(m).Association(a.Name())}
}

func (a channelBelongsToPrevSibling) Unscoped() *channelBelongsToPrevSibling {
	a.db = a.db.Unscoped()
	return &a
}

type channelBelongsToPrevSiblingTx struct{ tx *gorm.Association }

func (a channelBelongsToPrevSiblingTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelBelongsToPrevSiblingTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelBelongsToPrevSiblingTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelBelongsToPrevSiblingTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelBelongsToPrevSiblingTx) Clear() error {
	return a.tx.Clear()
}

func (a channelBelongsToPrevSiblingTx) Count() int64 {
	return a.tx.Count()
}

func (a channelBelongsToPrevSiblingTx) Unscoped() *channelBelongsToPrevSiblingTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c channelDo) FirstByID(id uint64) (result *model.Channel, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c channelDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update channels set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c channelDo) Debug() *channelDo {
	return c.withDO(c.DO.Debug())
}

func (c channelDo) WithContext(ctx context.Context) *channelDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c channelDo) ReadDB() *channelDo {
	return c.Clauses(dbresolver.Read)
}

func (c channelDo) WriteDB() *channelDo {
	return c.Clauses(dbresolver.Write)
}

func (c channelDo) Session(config *gorm.Session) *channelDo {
	return c.withDO(c.DO.Session(config))
}

func (c channelDo) Clauses(conds ...clause.Expression) *channelDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c channelDo) Returning(value interface{}, columns ...string) *channelDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c channelDo) Not(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c channelDo) Or(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c channelDo) Select(conds ...field.Expr) *channelDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c channelDo) Where(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c channelDo) Order(conds ...field.Expr) *channelDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c channelDo) Distinct(cols ...field.Expr) *channelDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c channelDo) Omit(cols ...field.Expr) *channelDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c channelDo) Join(table schema.Tabler, on ...field.Expr) *channelDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c channelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *channelDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c channelDo) RightJoin(table schema.Tabler, on ...field.Expr) *channelDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c channelDo) Group(cols ...field.Expr) *channelDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c channelDo) Having(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c channelDo) Limit(limit int) *channelDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c channelDo) Offset(offset int) *channelDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c channelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *channelDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c channelDo) Unscoped() *channelDo {
	return c.withDO(c.DO.Unscoped())
}

func (c channelDo) Create(values ...*model.Channel) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c channelDo) CreateInBatches(values []*model.Channel, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c channelDo) Save(values ...*model.Channel) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c channelDo) First() (*model.Channel, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) Take() (*model.Channel, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) Last() (*model.Channel, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) Find() ([]*model.Channel, error) {
	result, err := c.DO.Find()
	return result.([]*model.Channel), err
}

func (c channelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Channel, err error) {
	buf := make([]*model.Channel, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c channelDo) FindInBatches(result *[]*model.Channel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c channelDo) Attrs(attrs ...field.AssignExpr) *channelDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c channelDo) Assign(attrs ...field.AssignExpr) *channelDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c channelDo) Joins(fields ...field.RelationField) *channelDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c channelDo) Preload(fields ...field.RelationField) *channelDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c channelDo) FirstOrInit() (*model.Channel, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) FirstOrCreate() (*model.Channel, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) FindByPage(offset int, limit int) (result []*model.Channel, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c channelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c channelDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c channelDo) Delete(models ...*model.Channel) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *channelDo) withDO(do gen.Dao) *channelDo {
	c.DO = *do.(*gen.DO)
	return c
}
